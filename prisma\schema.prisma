// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id        String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  fullname  String
  clerkId   String   @unique
  type      String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  stripeId String?

  domains Domain[]

  campaign Campaign[]

  subscription Billings?
}

model Domain {
  id   String @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  name String
  icon String

  chatBot         ChatBot?
  helpdesk        HelpDesk[]
  filterQuestions FilterQuestions[]
  products        Product[]

  customer   Customer[]
  User       User?      @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId     String?    @db.Uuid
  Campaign   Campaign?  @relation(fields: [campaignId], references: [id])
  campaignId String?    @db.Uuid
}

model ChatBot {
  id             String  @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  welcomeMessage String?
  icon           String?
  background     String?
  textColor      String?
  helpdesk       Boolean @default(false)
  Domain         Domain? @relation(fields: [domainId], references: [id], onDelete: Cascade)
  domainId       String? @unique @db.Uuid
}

model Billings {
  id      String  @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  plan    Plans   @default(STANDARD)
  credits Int     @default(10)
  User    User?   @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId  String? @unique @db.Uuid
}

model HelpDesk {
  id       String @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  question String
  answer   String

  Domain   Domain? @relation(fields: [domainId], references: [id], onDelete: Cascade)
  domainId String? @db.Uuid
}

model FilterQuestions {
  id       String  @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  question String
  answered String?

  Domain   Domain? @relation(fields: [domainId], references: [id], onDelete: Cascade)
  domainId String? @db.Uuid
}

model CustomerResponses {
  id         String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  question   String
  answered   String?
  customer   Customer @relation(fields: [customerId], references: [id], onDelete: Cascade)
  customerId String   @db.Uuid
}

model Customer {
  id    String  @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  email String? 

  questions CustomerResponses[]

  chatRoom ChatRoom[]

  booking Bookings[]

  Domain   Domain? @relation(fields: [domainId], references: [id], onDelete: Cascade)
  domainId String? @db.Uuid
}

model ChatRoom {
  id         String    @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  live       Boolean   @default(false)
  mailed     Boolean   @default(false)
  createdAt  DateTime  @default(now())
  updatedAt  DateTime  @updatedAt
  Customer   Customer? @relation(fields: [customerId], references: [id], onDelete: Cascade)
  customerId String?   @db.Uuid

  message ChatMessage[]
}

model ChatMessage {
  id         String    @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  message    String
  role       Role?
  createdAt  DateTime  @default(now())
  updatedAt  DateTime  @updatedAt
  ChatRoom   ChatRoom? @relation(fields: [chatRoomId], references: [id], onDelete: Cascade)
  chatRoomId String?   @db.Uuid
  seen       Boolean   @default(false)
}

model Bookings {
  id         String    @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  date       DateTime
  slot       String
  email      String
  Customer   Customer? @relation(fields: [customerId], references: [id], onDelete: Cascade)
  customerId String?   @db.Uuid
  domainId   String?   @db.Uuid
  createdAt  DateTime  @default(now())
}

model Campaign {
  id        String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  name      String
  customers String[]
  domain    Domain[]
  template  String?
  User      User?    @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId    String?  @db.Uuid
  createdAt DateTime @default(now())
}

model Product {
  id        String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  name      String
  price     Int
  image     String
  createdAt DateTime @default(now())
  Domain    Domain?  @relation(fields: [domainId], references: [id], onDelete: Cascade)
  domainId  String?  @db.Uuid
}

enum Plans {
  STANDARD
  PRO
  ULTIMATE
}

enum Role {
  user
  assistant
}
