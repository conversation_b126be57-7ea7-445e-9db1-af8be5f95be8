import {
  Sheet,
  SheetContent,
  SheetD<PERSON><PERSON>,
  She<PERSON><PERSON>eader,
  She<PERSON><PERSON><PERSON><PERSON>,
  She<PERSON><PERSON>rigger,
} from '@/components/ui/sheet'

type SideSheetProps = {
  trigger: React.ReactNode
  title: string
  description: string
  children: React.ReactNode
  className?: string
}

export const SideSheet = ({
  trigger,
  title,
  description,
  children,
  className,
}: SideSheetProps) => {
  return (
    <Sheet>
      <SheetTrigger className={className}>{trigger}</SheetTrigger>
      <SheetContent>
        <SheetHeader>
          <SheetTitle>{title}</SheetTitle>
          <SheetDescription>{description}</SheetDescription>
        </SheetHeader>
        {children}
      </SheetContent>
    </Sheet>
  )
}
